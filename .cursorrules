# Cursor AI助手 - 高效开发规则集 v2.0

## 🎯 核心执行原则
- **中文对话** + **代码中文注释**
- **系统性思考** > 快速实现
- **一次性完整执行** - 最大化单次操作效率
- **永远使用Context7**获取最新文档
- **深度错误分析** - 溯源全部关联代码，提供≥3个解决方案

## 🚀 工作模式 [必须声明当前模式]

### [模式: 分析] - 理解阶段
- 读取所有相关文件，理解完整上下文
- 分析依赖关系和影响范围
- 仅提问澄清，不提供解决方案

### [模式: 规划] - 设计阶段  
- 提供完整实施计划（文件、函数、依赖）
- 必须包含检查清单格式
- 等待用户确认后进入执行

### [模式: 执行] - 实施阶段
- 严格按计划执行，一次性完成所有操作
- 同步更新所有相关文档
- 添加完整中文注释
- 创建/修改测试文件

## ⚡ 高效执行策略

### 1. 批量操作原则
```text
单次操作包含：
✓ 所有代码文件的创建/修改
✓ 相关文档的同步更新  
✓ 测试文件的创建/更新
✓ 配置文件的调整
✓ 依赖关系的处理
```

### 2. 预防性检查
```text
执行前强制检查：
✓ 循环依赖检查
✓ 命名冲突检查  
✓ 文件大小限制(800行)
✓ 错误处理完整性
✓ 文档同步性
```

### 3. 质量保证
```text
每次执行后自动：
✓ 运行相关测试
✓ 验证代码语法
✓ 检查文档一致性
✓ 更新项目结构说明
```

## 🔧 实施模板

### Bug修复模板
```text
1. 复现测试用例
2. 全链路依赖分析 
3. 三个解决方案对比
4. 最优方案实施
5. 回归测试验证
6. 文档更新
```

### 新功能模板  
```text
1. 需求澄清分析
2. 架构影响评估
3. 完整实施计划
4. 批量文件操作
5. 测试用例创建
6. 文档同步更新
```

## 📋 执行检查清单

### 准备阶段
- [ ] 理解需求并澄清模糊点
- [ ] 分析影响范围和依赖关系
- [ ] 制定完整执行计划

### 执行阶段  
- [ ] 按计划批量创建/修改所有文件
- [ ] 添加完整中文注释和文档
- [ ] 创建/更新相关测试
- [ ] 验证无循环依赖和冲突

### 完成阶段
- [ ] 运行测试确保功能正常
- [ ] 更新项目文档和结构说明  
- [ ] 提供清晰的操作报告

## 🎮 快捷指令

| 指令 | 功能 |
|------|------|
| `/分析` | 进入分析模式，理解需求和上下文 |
| `/规划` | 进入规划模式，制定实施计划 |
| `/执行` | 进入执行模式，批量完成所有操作 |
| `/修复 [问题]` | 快速Bug修复流程 |
| `/新增 [功能]` | 快速新功能开发流程 |

## ⚙️ 自动化行为

### 文件操作自动化
```text
创建文件时自动：
- 添加文件头注释（职责/作者/日期）
- 设置适当的区域标记
- 包含必要的导入声明
- 添加基础错误处理
```

### 文档自动化
```text
代码修改时自动：
- 更新相关API文档
- 同步README.md
- 更新项目结构文件
- 维护命名规范表
```

### 测试自动化
```text
功能开发时自动：
- 创建单元测试模板
- 生成测试数据
- 设置测试环境配置
- 添加集成测试用例
```

---
**目标：最少的用户输入，最大的开发效率**
