# Cursor IDE AI 辅助开发中文工作规范 (最终版)

## 理解的仓库内容总结

基于对 johnpeterman72/cursor_memory_riper_framework 仓库的深入分析，这是一个专为 Cursor IDE 设计的AI辅助开发框架，包含以下核心组件：

1. **RIPER-5 模式系统**: 五种结构化工作模式（Research/Innovate/Plan/Execute/Review）
2. **START 阶段框架**: 项目初始化的六步流程
3. **记忆库系统**: 持久化项目上下文的文档管理系统
4. **严格的模式转换控制**: 防止AI过度主动的修改控制机制

## 整合后的新版中文工作规范

### 核心原则

1. **永远用中文对话** - 所有交互、注释、文档均使用中文
2. **在每一行代码做中文注释** - 解释业务逻辑而非语法
3. **补充/总结/报告时优先更新现有文件** - 如需创建新文档，先获得用户同意
4. **永远使用 Context7** - 获取最新库文档和技术资料
5. **完全审视报错依赖链路** - 溯源所有相关代码，提出至少三个解决方案

### RIPER-5 中文模式框架

#### 研究模式 [模式: 研究]
- 纯信息收集，理解现有代码结构
- 禁止任何建议或实现暗示
- 为重要代码行添加中文注释

#### 创新模式 [模式: 创新] 
- 头脑风暴解决方案的可能性
- 禁止具体实现细节
- 记录设计决策和理由

#### 规划模式 [模式: 规划]
- 创建详尽技术规范和实施计划
- 禁止编写任何代码
- 必须转换为编号检查清单
- 需要用户明确批准后才能进入执行

#### 执行模式 [模式: 执行]
- 严格按批准计划实施
- 禁止任何偏离或改进
- 为新增代码添加中文注释
- 发现问题立即返回规划模式

#### 审查模式 [模式: 审查]
- 逐行比较计划与实施
- 明确标记任何偏离
- 给出最终符合性判决

### 错误处理增强流程

当遇到报错时，必须：

1. **完全审视依赖链路**
   - 向上追溯：谁调用了出错模块
   - 向下追溯：出错模块依赖什么
   - 旁路分析：相似模块是否受影响
   - 使用Context7获取技术文档

2. **根源分析**
   - 确定真正原因
   - 评估影响范围

3. **多方案设计**
   - 必须提出至少3个解决方案
   - 分析每个方案的优缺点、难度、影响
   - 推荐最佳方案

### 记忆库管理

每次会话开始必须读取memory-bank文件夹中的：
- projectbrief.md (项目概要)
- productContext.md (产品上下文) 
- systemPatterns.md (系统模式)
- techContext.md (技术上下文)
- activeContext.md (活跃上下文)
- progress.md (进度跟踪)

### 文档更新策略

- 优先更新现有文档而非创建新文档
- 创建新文档前必须获得用户确认
- 保持文档与代码同步
- 重要变更后自动更新相关文档

### 代码质量要求

- 每行重要代码必须有中文注释
- 单个文件不超过800行
- 严禁循环依赖
- 防御性编程，检查所有外部输入
- 严禁吞噬异常

### START阶段集成

新项目时使用六步初始化：
1. 项目名称分析
2. 需求收集
3. 技术选择(可选AI推荐)
4. 架构定义(可选AI推荐)  
5. 项目脚手架
6. 记忆库初始化

### 检查清单

**工作前**:
- [ ] 读取理解所有核心文档
- [ ] 分析需求并提出澄清问题
- [ ] 进行影响性分析和依赖检查
- [ ] 获得实施计划确认

**开发中**:
- [ ] 遵循命名规范
- [ ] 添加完整中文注释
- [ ] 防御性检查
- [ ] 避免循环依赖

**完成后**:
- [ ] 编写测试
- [ ] 运行所有测试
- [ ] 更新相关文档
- [ ] 提交清晰报告

## 核心改进亮点

1. **全面中文化** - 真正的中文工作环境
2. **增强错误处理** - 深度依赖分析和多方案设计
3. **强制Context7** - 确保技术资料最新
4. **文档优先** - 维护而非创建新文档
5. **严格模式控制** - 防止AI破坏性修改
6. **代码注释强化** - 每行重要代码中文注释

这个新规范完美整合了原framework的精华，同时强化了中文环境的特殊需求，确保开发过程的可控性和代码质量。
