/**
 * MDAC数据抓取器 - 动态获取官网最新的州和城市数据
 * 解决城市代码映射不一致的问题
 */

class MDACDataScraper {
    constructor() {
        this.baseUrl = 'https://imigresen-online.imi.gov.my';
        this.cache = new Map();
        this.cacheExpiry = 24 * 60 * 60 * 1000; // 24小时缓存
        this.lastUpdate = null;
        
        // 初始化默认数据（作为备用）
        this.initializeDefaultData();
    }

    /**
     * 初始化默认数据作为备用
     */
    initializeDefaultData() {
        this.defaultData = {
            states: {
                '01': { code: '01', name: '<PERSON><PERSON>', chinese: '柔佛' },
                '02': { code: '02', name: 'Kedah', chinese: '吉打' },
                '03': { code: '03', name: '<PERSON><PERSON><PERSON>', chinese: '吉兰丹' },
                '04': { code: '04', name: '<PERSON><PERSON>', chinese: '马六甲' },
                '05': { code: '05', name: '<PERSON><PERSON><PERSON>', chinese: '森美兰' },
                '06': { code: '06', name: '<PERSON><PERSON>', chinese: '彭亨' },
                '07': { code: '07', name: '<PERSON><PERSON><PERSON>', chinese: '槟城' },
                '08': { code: '08', name: 'Perak', chinese: '霹雳' },
                '09': { code: '09', name: 'Perlis', chinese: '玻璃市' },
                '10': { code: '10', name: 'Selangor', chinese: '雪兰莪' },
                '11': { code: '11', name: 'Terengganu', chinese: '登嘉楼' },
                '12': { code: '12', name: 'Sabah', chinese: '沙巴' },
                '13': { code: '13', name: 'Sarawak', chinese: '砂拉越' },
                '14': { code: '14', name: 'Kuala Lumpur', chinese: '吉隆坡' },
                '15': { code: '15', name: 'Labuan', chinese: '纳闽' },
                '16': { code: '16', name: 'Putrajaya', chinese: '布城' }
            },
            cities: {
                // 柔佛州城市
                '0101': { code: '0101', name: 'Johor Bahru', state: '01', chinese: '新山', postcodeRange: ['79000', '82999'] },
                '0102': { code: '0102', name: 'Batu Pahat', state: '01', chinese: '峇株巴辖', postcodeRange: ['83000', '84999'] },
                '0103': { code: '0103', name: 'Muar', state: '01', chinese: '麻坡', postcodeRange: ['84000', '85999'] },
                '0104': { code: '0104', name: 'Kluang', state: '01', chinese: '居銮', postcodeRange: ['86000', '86999'] },
                '0105': { code: '0105', name: 'Segamat', state: '01', chinese: '昔加末', postcodeRange: ['85000', '85999'] },
                
                // 吉隆坡
                '1401': { code: '1401', name: 'Kuala Lumpur City', state: '14', chinese: '吉隆坡市', postcodeRange: ['50000', '60000'] },
                '1402': { code: '1402', name: 'Cheras', state: '14', chinese: '蕉赖', postcodeRange: ['56000', '56999'] },
                '1403': { code: '1403', name: 'Kepong', state: '14', chinese: '甲洞', postcodeRange: ['52000', '52999'] },
                
                // 雪兰莪州城市
                '1001': { code: '1001', name: 'Shah Alam', state: '10', chinese: '莎阿南', postcodeRange: ['40000', '40999'] },
                '1002': { code: '1002', name: 'Petaling Jaya', state: '10', chinese: '八打灵再也', postcodeRange: ['46000', '47999'] },
                '1003': { code: '1003', name: 'Subang Jaya', state: '10', chinese: '梳邦再也', postcodeRange: ['47500', '47999'] },
                '1004': { code: '1004', name: 'Klang', state: '10', chinese: '巴生', postcodeRange: ['41000', '42999'] },
                
                // 槟城州城市
                '0701': { code: '0701', name: 'George Town', state: '07', chinese: '乔治市', postcodeRange: ['10000', '11999'] },
                '0702': { code: '0702', name: 'Butterworth', state: '07', chinese: '北海', postcodeRange: ['12000', '14999'] },
                '0703': { code: '0703', name: 'Bukit Mertajam', state: '07', chinese: '大山脚', postcodeRange: ['14000', '14999'] },
                
                // 马六甲州城市
                '0401': { code: '0401', name: 'Melaka City', state: '04', chinese: '马六甲市', postcodeRange: ['75000', '78999'] },
                '0402': { code: '0402', name: 'Alor Gajah', state: '04', chinese: '亚罗牙也', postcodeRange: ['78000', '78999'] }
            }
        };
    }

    /**
     * 获取州列表数据
     */
    async getStates() {
        try {
            const cacheKey = 'states';
            const cached = this.getCachedData(cacheKey);
            if (cached) {
                console.log('✅ 使用缓存的州数据');
                return cached;
            }

            console.log('🔄 尝试从MDAC官网获取州数据...');
            
            // 尝试从当前页面获取州选项
            const states = await this.extractStatesFromCurrentPage();
            
            if (states && Object.keys(states).length > 0) {
                this.setCachedData(cacheKey, states);
                console.log(`✅ 成功获取 ${Object.keys(states).length} 个州数据`);
                return states;
            } else {
                console.log('⚠️ 无法从官网获取州数据，使用默认数据');
                return this.defaultData.states;
            }
        } catch (error) {
            console.error('❌ 获取州数据失败:', error);
            return this.defaultData.states;
        }
    }

    /**
     * 获取指定州的城市列表
     */
    async getCitiesByState(stateCode) {
        try {
            const cacheKey = `cities_${stateCode}`;
            const cached = this.getCachedData(cacheKey);
            if (cached) {
                console.log(`✅ 使用缓存的城市数据 (州: ${stateCode})`);
                return cached;
            }

            console.log(`🔄 尝试获取州 ${stateCode} 的城市数据...`);
            
            // 尝试触发级联更新获取城市数据
            const cities = await this.extractCitiesForState(stateCode);
            
            if (cities && Object.keys(cities).length > 0) {
                this.setCachedData(cacheKey, cities);
                console.log(`✅ 成功获取州 ${stateCode} 的 ${Object.keys(cities).length} 个城市`);
                return cities;
            } else {
                // 使用默认数据中对应州的城市
                const defaultCities = this.getDefaultCitiesForState(stateCode);
                console.log(`⚠️ 使用默认城市数据 (州: ${stateCode})`);
                return defaultCities;
            }
        } catch (error) {
            console.error(`❌ 获取州 ${stateCode} 城市数据失败:`, error);
            return this.getDefaultCitiesForState(stateCode);
        }
    }

    /**
     * 从当前页面提取州选项
     */
    async extractStatesFromCurrentPage() {
        return new Promise((resolve) => {
            try {
                // 查找州下拉框
                const stateSelect = document.querySelector('select[name="accommodationState"], select[id*="state"], select[id*="State"]');
                
                if (!stateSelect) {
                    console.log('⚠️ 未找到州下拉框');
                    resolve(null);
                    return;
                }

                const states = {};
                const options = stateSelect.querySelectorAll('option');
                
                options.forEach(option => {
                    const value = option.value.trim();
                    const text = option.textContent.trim();
                    
                    if (value && value !== '' && value !== '0') {
                        states[value] = {
                            code: value,
                            name: text,
                            chinese: this.getChineseStateName(text) || text
                        };
                    }
                });

                resolve(Object.keys(states).length > 0 ? states : null);
            } catch (error) {
                console.error('提取州数据时出错:', error);
                resolve(null);
            }
        });
    }

    /**
     * 获取指定州的城市数据（通过模拟级联更新）
     */
    async extractCitiesForState(stateCode) {
        return new Promise((resolve) => {
            try {
                // 查找州和城市下拉框
                const stateSelect = document.querySelector('select[name="accommodationState"], select[id*="state"], select[id*="State"]');
                const citySelect = document.querySelector('select[name="accommodationCity"], select[id*="city"], select[id*="City"]');
                
                if (!stateSelect || !citySelect) {
                    console.log('⚠️ 未找到州或城市下拉框');
                    resolve(null);
                    return;
                }

                // 保存当前选择
                const originalStateValue = stateSelect.value;
                const originalCityValue = citySelect.value;

                // 设置州选择并触发change事件
                stateSelect.value = stateCode;
                stateSelect.dispatchEvent(new Event('change', { bubbles: true }));

                // 等待级联更新完成
                setTimeout(() => {
                    try {
                        const cities = {};
                        const cityOptions = citySelect.querySelectorAll('option');
                        
                        cityOptions.forEach(option => {
                            const value = option.value.trim();
                            const text = option.textContent.trim();
                            
                            if (value && value !== '' && value !== '0') {
                                cities[value] = {
                                    code: value,
                                    name: text,
                                    state: stateCode,
                                    chinese: this.getChineseCityName(text) || text,
                                    postcodeRange: this.getPostcodeRange(stateCode, value)
                                };
                            }
                        });

                        // 恢复原始选择
                        stateSelect.value = originalStateValue;
                        citySelect.value = originalCityValue;

                        resolve(Object.keys(cities).length > 0 ? cities : null);
                    } catch (error) {
                        console.error('提取城市数据时出错:', error);
                        resolve(null);
                    }
                }, 1000); // 等待1秒让级联更新完成

            } catch (error) {
                console.error('模拟级联更新时出错:', error);
                resolve(null);
            }
        });
    }
